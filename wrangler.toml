name = "worker"
main = "_worker.js"
compatibility_date = "2025-09-05"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "worker"

[env.dev]
name = "worker-dev"

# 环境变量配置示例（根据需要取消注释并设置）
[vars]
# UUID = "your-uuid-here"
# PROXYIP = "your-proxy-ip"
# DNS64 = "your-dns64-server"
# SOCKS5 = "your-socks5-proxy"

# KV 命名空间（如果需要）
# [[kv_namespaces]]
# binding = "KV"
# id = "your-kv-namespace-id"

# 资源限制
[limits]
cpu_ms = 50
